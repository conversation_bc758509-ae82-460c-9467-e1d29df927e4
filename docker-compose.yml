services:
  legal-backend:
    build: .
    ports:
      - "${HOST_PORT:-8020}:8000"
    env_file: .env
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Security settings
    security_opt:
      - no-new-privileges:true
    read_only: false  # Set to true if you want read-only filesystem
    # Resource limits for production
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'