from app.core.security import require
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from app.v1.api.chat.core.tools import LegalTools
from app.v1.api.chat.core.utils.process_najirs import _process_sources
from app.v1.api.chat.core.utils.process_citation import process_citation

from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage, AIMessage
from llama_index.llms.gemini import Gemini
from llama_index.core.base.llms.types import CompletionResponse
from google import genai
from google.genai import types

from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from fastapi import APIRouter, Depends, Query
from typing import Annotated, List, Optional, Dict, Any
import json
import asyncio
import time
from datetime import datetime, timezone
import urllib.parse
import re

logger = StructuredLogger(__name__)
router = APIRouter()

class OptimizedAgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    original_query: str
    is_legal_query: bool
    refined_query: str
    tools_to_run: List[str]
    tool_responses: Dict[str, Any]
    all_sources: List[dict]
    final_response: str
    citation_mapping: Dict[str, Any]
    step_times: Dict[str, float]

def get_current_timestamp():
    """Get current UTC timestamp in ISO format"""
    return datetime.now(timezone.utc).isoformat()

class OptimizedLegalAgent:
    def __init__(self, current_user: CurrentUser):
        logger.info("🚀 OPTIMIZED AGENT INIT - Starting with Gemini-first approach")
        
        self.current_user = current_user
        self.llm_openai = current_user.llm.openai
        self.llm_gemini = current_user.llm.gemini
        self.gemini = genai.Client(api_key=current_user.llm.apikeys["GEMINI_API_KEY"])
        self.tools = []
        self.legal_tools = None
        self.checkpointer = AsyncMongoDBSaver(
            client=current_user.db.async_client,
            db_name=current_user.db.read_db.name,
            collection_name="checkpoints"
        )
        self.graph = None
        
        logger.info("✅ OPTIMIZED AGENT INIT COMPLETE - Gemini ready")

    async def setup_tools_async(self):
        """Setup tools asynchronously"""
        if self.tools:
            return
            
        user_id = str(self.current_user.user.id)
        logger.info(f"🔨 ASYNC TOOLS SETUP - For user: {user_id}")
        
        try:
            self.legal_tools = LegalTools()
            self.tools = self.legal_tools.create_tools(self.current_user)
            logger.info(f"✅ ASYNC TOOLS READY - Created {len(self.tools)} tools")
        except Exception as e:
            logger.error(f"❌ ASYNC TOOLS SETUP FAILED: {e}")
            self.tools = []

    async def step1_smart_analysis(self, state: OptimizedAgentState):
        """STEP 1: Smart query analysis with Gemini"""
        step_start = time.time()
        logger.info("🧠 STEP 1 START - Smart Gemini analysis")

        user_query = state.get("original_query", state["messages"][-1].content)
        logger.info(f"📝 ANALYZING QUERY: {user_query}")

        # Ensure tools are ready
        await self.setup_tools_async()

        analysis_prompt_doc = await self.current_user.db.read_db.prompts.find_one({"name": "analysis_prompt"}, {"prompt": 1})
        analysis_prompt = analysis_prompt_doc.get("prompt", """
Analyze the following user query and determine the optimal research strategy.
Query: "{user_query}"
Available research tools:
- constitution_search: Constitution of Nepal 2015, constitutional articles, fundamental rights, and constitutional provisions
- act_search: Statutes, laws, regulations, acts of Nepal
- najir_search: Case law, precedents, court decisions from Nepali courts
- najir_summary: Legal concept overviews, general summaries of Nepali law

Instructions for the assistant:
1. Understand the query fully, whether it is written in English or Nepali using Latin/English letters.
2. Ensure the refined_query is in Nepali .( Make the query for a retreiver with keywords)
3. Determine if this is a legal query (true/false). Consider topics related to property, inheritance, contracts, fraud, civil/criminal disputes, constitutional rights, or any legal matters.
4. If legal, enhance the query by adding relevant legal terminology, synonyms, and context to ensure accurate research.
5. Decide which research tools are needed to provide a complete and accurate answer.
6. **Priority Order**: When constitutional issues are involved, always include constitution_search first, followed by act_search for relevant statutes, then najir_search for case law.

Respond ONLY with JSON:
{{
    "is_legal": true/false,
    "refined_query": "enhanced query with legal context",
    "tools_needed": ["constitution_search", "act_search", "najir_search", "najir_summary"],
    "reasoning": "brief explanation"
}}
For non-legal queries, set is_legal to false and tools_needed to [].
For constitutional law queries, always include constitution_search as the first priority.
For legal queries, use multiple tools in parallel but prioritize constitutional sources.
        """).format(user_query=user_query)
        
        logger.info(f"📝 ANALYSIS PROMPT: {analysis_prompt}")
        
        try:
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=analysis_prompt),
                    ],
                ),
            ]
            analysis_response = self.gemini.models.generate_content(
                model="gemini-2.0-flash", 
                contents=contents
            )
            
            # Parse JSON response
            response_text = analysis_response.text.strip()
            if '```json' in response_text:
                response_text = response_text.split('```json')[1].split('```')[0].strip()
            elif '```' in response_text:
                response_text = response_text.split('```')[1].strip()
            
            analysis_data = json.loads(response_text)
            
            is_legal = analysis_data.get("is_legal", False)
            refined_query = analysis_data.get("refined_query", user_query)
            tools_needed = analysis_data.get("tools_needed", [])
            reasoning = analysis_data.get("reasoning", "")
            
            logger.info(f"🏛️ LEGAL QUERY: {is_legal}")
            logger.info(f"🔧 REFINED: {refined_query}")
            logger.info(f"🛠️ TOOLS: {tools_needed}")
            logger.info(f"💡 REASONING: {reasoning}")
            
        except Exception as e:
            logger.warning(f"Analysis parsing failed: {e}, defaulting to legal query")
            is_legal = True
            refined_query = user_query
            tools_needed = ["najir_search", "act_search", "najir_summary"]

        # Handle non-legal queries immediately
        if not is_legal:
            step_time = time.time() - step_start
            
            non_legal_response = f"""I specialize in Nepali legal research and analysis. Your query "{user_query}" doesn't appear to be legal-related.

I can help you with:
- Nepali case law and court precedents
- Legal statutes and regulations
- Legal procedures and rights
- Legal analysis and guidance

Please ask a legal question, and I'll provide comprehensive research with proper citations."""
            
            logger.info(f"⚠️ NON-LEGAL QUERY - Direct response in {step_time:.2f}s")
            
            return {
                "is_legal_query": False,
                "refined_query": refined_query,
                "tools_to_run": [],
                "final_response": non_legal_response,
                "citation_mapping": {},
                "all_sources": [],
                "step_times": {"step1_analysis": step_time}
            }

        step_time = time.time() - step_start
        logger.info(f"✅ STEP 1 COMPLETE - Analysis done in {step_time:.2f}s")

        return {
            "is_legal_query": is_legal,
            "refined_query": refined_query,
            "tools_to_run": tools_needed,
            "step_times": {"step1_analysis": step_time}
        }

    async def step2_parallel_research(self, state: OptimizedAgentState):
        """STEP 2: Execute all tools in parallel"""
        step_start = time.time()
        logger.info("⚡ STEP 2 START - Parallel research execution")

        # Skip if non-legal or no tools needed
        if not state.get("is_legal_query", True) or state.get("final_response"):
            logger.info("⏭️ SKIPPING TOOLS - Non-legal query")
            step_time = time.time() - step_start
            return {
                "tool_responses": {},
                "all_sources": [],
                "step_times": {**state.get("step_times", {}), "step2_research": step_time}
            }

        tools_to_run = state.get("tools_to_run", [])
        refined_query = state.get("refined_query", state.get("original_query", ""))
        
        logger.info(f"🔍 PARALLEL SEARCH: {len(tools_to_run)} tools with query: {refined_query}")

        # Create tool execution tasks
        tasks = []
        tool_names = []
        
        for tool_name in tools_to_run:
            tool_instance = None
            for tool in self.tools:
                if tool.name == tool_name:
                    tool_instance = tool
                    break
            
            if tool_instance:
                tasks.append(tool_instance.arun(refined_query))
                tool_names.append(tool_name)
                logger.info(f"📋 QUEUED: {tool_name}")
            else:
                logger.warning(f"❌ TOOL NOT FOUND: {tool_name}")

        # Execute all tools in parallel
        logger.info(f"🚀 EXECUTING {len(tasks)} tools simultaneously")
        
        results = []
        if tasks:
            try:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                logger.info(f"📊 RECEIVED {len(results)} results")
            except Exception as e:
                logger.error(f"❌ PARALLEL EXECUTION FAILED: {e}")

        # Process results - FIXED: Preserve id_ field and ensure continuous citation numbering
        tool_responses = {}
        all_sources = []
        citation_counter = 1

        for i, (result, tool_name) in enumerate(zip(results, tool_names)):
            if isinstance(result, Exception):
                logger.error(f"❌ {tool_name.upper()} ERROR: {result}")
                continue

            try:
                if isinstance(result, str):
                    parsed_result = json.loads(result)
                else:
                    parsed_result = result

                response_text = parsed_result.get('response', '')
                sources = parsed_result.get('sources', [])
                
                tool_responses[tool_name] = {
                    "response": response_text,
                    "sources": sources
                }
                
                # FIXED: Process sources and preserve id_ field
                for source in sources:
                    metadata = source.get("metadata", {})
                    source_data = {
                        "id_": source.get("id_", f"{tool_name}_{citation_counter}"),  # Preserve or generate id_
                        "citation_number": citation_counter,
                        "tool": tool_name,
                        "text": source.get("text", ""),
                        "score": source.get("score", 0.0),
                        "metadata": metadata
                    }
                    all_sources.append(source_data)
                    citation_counter += 1
                
                logger.info(f"✅ {tool_name.upper()}: {len(sources)} sources, response: {len(response_text)} chars")
                
            except Exception as e:
                logger.error(f"❌ PROCESSING {tool_name.upper()} RESULT FAILED: {e}")

        step_time = time.time() - step_start
        logger.info(f"✅ STEP 2 COMPLETE - {len(all_sources)} total sources in {step_time:.2f}s")

        return {
            "tool_responses": tool_responses,
            "all_sources": all_sources,
            "step_times": {**state.get("step_times", {}), "step2_research": step_time}
        }

    def _validate_and_clean_citations(self, response_text: str, citation_mapping: Dict[str, Any]) -> str:
        """Remove citations from response that don't exist in citation_mapping"""
        if not response_text or not citation_mapping:
            return response_text
        
        # Find all citation patterns [1], [2], etc.
        citation_pattern = r'\[(\d+)\]'
        citations_in_response = set(re.findall(citation_pattern, response_text))
        valid_citations = set(citation_mapping.keys())
        
        invalid_citations = citations_in_response - valid_citations
        
        if invalid_citations:
            logger.warning(f"⚠️ REMOVING INVALID CITATIONS: {invalid_citations}")
            
            # Remove invalid citations from response
            for invalid_cite in invalid_citations:
                response_text = re.sub(rf'\[{invalid_cite}\]', '', response_text)
            
            # Clean up any double spaces or formatting issues
            response_text = re.sub(r'\s+', ' ', response_text).strip()
        
        return response_text

    async def step3_intelligent_synthesis(self, state: OptimizedAgentState):
        """STEP 3: Intelligent response synthesis with Gemini"""
        step_start = time.time()
        logger.info("🎯 STEP 3 START - Intelligent Gemini synthesis")

        # Return existing response if already set
        if state.get("final_response"):
            step_time = time.time() - step_start
            return {
                "final_response": state.get("final_response"),
                "citation_mapping": state.get("citation_mapping", {}),
                "step_times": {**state.get("step_times", {}), "step3_synthesis": step_time}
            }

        user_query = state.get("original_query", "")
        tool_responses = state.get("tool_responses", {})
        all_sources = state.get("all_sources", [])

        logger.info(f"📝 SYNTHESIZING: {len(tool_responses)} tool responses, {len(all_sources)} sources")

        # Build comprehensive context
        research_context = ""
        for tool_name, data in tool_responses.items():
            research_context += f"\n=== {tool_name.upper()} RESULTS ===\n"
            research_context += data.get("response", "")
            research_context += "\n"

        # FIXED: Build source context with id_ preservation and better structure
        sources_context = ""
        valid_citation_mapping = {}  # Track which citations are actually valid
        
        for source in all_sources[:20]:  # Limit to top 20 sources to avoid token limits
            citation_num = str(source['citation_number'])
            
            sources_context += f"\n[{citation_num}] {source['tool'].upper()}:\n"
            source_text = source['text'][:400] + "..." if len(source['text']) > 400 else source['text']
            sources_context += f"Text: {source_text}\n"
            
            metadata = source.get('metadata', {})
            if metadata.get('title'):
                sources_context += f"Title: {metadata['title']}\n"
            if metadata.get('court_type'):
                sources_context += f"Court: {metadata['court_type']}\n"
            if metadata.get('year'):
                sources_context += f"Year: {metadata['year']}\n"
            sources_context += "\n"
            
            # FIXED: Build valid citation mapping with id_ preserved
            valid_citation_mapping[citation_num] = {
                "id_": source.get("id_", f"{source['tool']}_{citation_num}"),  # Preserve id_
                "tool": source["tool"],
                "text": source["text"],
                "score": source.get("score", 0.0),
                "metadata": metadata
            }

        # Get synthesis prompt from database
        synthesis_prompt_doc = await self.current_user.db.read_db.prompts.find_one({"name": "synthesis_prompt"}, {"prompt": 1})
        synthesis_prompt = synthesis_prompt_doc.get("prompt", """
You are an expert Nepali legal researcher. Using only the provided research results and source materials, provide a detailed legal analysis.
**Do Not Describe or Mention the users query at any cost.**
User Query: {user_query}
Research Results: {research_context}
Source Materials: {sources_context}
Instructions:
1. **Priority Structure**: Organize your analysis with this hierarchy:
   - **Constitutional Provisions** (from constitution_search) - Highest priority
   - **Statutory Law** (from act_search) - Second priority  
   - **Case Law** (from najir_search) - Third priority
   - **Legal Summaries** (from najir_summary) - Supporting material
2. Provide a comprehensive legal analysis strictly based on the research and sources.
3. Do not mention, describe, or reference the user's query in any form.
4. Structure your response with clear sections:
   - **Constitutional Analysis** (if constitution_search results exist)
   - **Applicable Statutes** (if act_search results exist)
   - **Relevant Case Law** (if najir_search results exist)
   - **Legal Analysis and Conclusions**
5. Include specific legal provisions, judicial precedents, and practical guidance.
6. Use proper citations [1], [2], [3], etc., corresponding to the numbered sources and the citation numbers should be in English numbers.
7. Be thorough yet concise, ensuring all claims are properly cited.
8. Respond in the Nepali Language
9. IMPORTANT: Only use citation numbers that exist in the provided sources. Available citations: {available_citations}
        """).format(
            user_query=user_query,
            research_context=research_context,
            sources_context=sources_context,
            available_citations=", ".join([f"[{k}]" for k in valid_citation_mapping.keys()])
        )
        
        logger.info(f"📝 SYNTHESIS PROMPT: {synthesis_prompt}")
        
        try:
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=synthesis_prompt),
                    ],
                ),
            ]
            synthesis_response = self.gemini.models.generate_content(
                model="gemini-2.0-flash", 
                contents=contents
            )
            final_response = synthesis_response.text
            
            # FIXED: Validate and clean citations
            final_response = self._validate_and_clean_citations(final_response, valid_citation_mapping)
            
            step_time = time.time() - step_start
            logger.info(f"✅ STEP 3 COMPLETE - Synthesis ready in {step_time:.2f}s")
            
            return {
                "final_response": final_response,
                "citation_mapping": valid_citation_mapping,  # Use validated mapping
                "step_times": {**state.get("step_times", {}), "step3_synthesis": step_time}
            }
            
        except Exception as e:
            step_time = time.time() - step_start
            logger.error(f"❌ SYNTHESIS FAILED: {e}")
            
            fallback_response = f"I apologize, but I encountered an error while processing your legal query: {str(e)}"
            
            return {
                "final_response": fallback_response,
                "citation_mapping": {},
                "step_times": {**state.get("step_times", {}), "step3_synthesis": step_time}
            }

    def should_run_research(self, state: OptimizedAgentState) -> str:
        """Smart routing decision"""
        if state.get("final_response"):  # Non-legal query already handled
            return "END"
        elif state.get("is_legal_query", True) and state.get("tools_to_run"):
            return "step2_research"
        else:
            return "step3_synthesis"

    def build_graph(self):
        """Build optimized 3-step workflow"""
        logger.info("🏗️ BUILDING OPTIMIZED WORKFLOW")

        builder = StateGraph(OptimizedAgentState)

        # Add nodes
        builder.add_node("step1_analysis", self.step1_smart_analysis)
        builder.add_node("step2_research", self.step2_parallel_research)
        builder.add_node("step3_synthesis", self.step3_intelligent_synthesis)

        # Build flow
        builder.add_edge(START, "step1_analysis")
        
        builder.add_conditional_edges(
            "step1_analysis",
            self.should_run_research,
            {
                "step2_research": "step2_research",
                "step3_synthesis": "step3_synthesis",
                "END": "__end__"
            }
        )
        
        builder.add_edge("step2_research", "step3_synthesis")

        self.graph = builder.compile(checkpointer=self.checkpointer)
        logger.info("✅ OPTIMIZED WORKFLOW READY")
        return self.graph

    async def run(self, query: str, thread_id: Optional[str] = None):
        """Execute optimized workflow"""
        workflow_start = time.time()
        user_id = str(self.current_user.user.id)
        
        logger.info("🚀 OPTIMIZED WORKFLOW START")
        logger.info(f"👤 USER: {user_id}")
        logger.info(f"❓ QUERY: {query}")
        
        # Ensure everything is ready
        if not self.graph:
            self.build_graph()
        
        config = {
            "configurable": {"thread_id": thread_id or user_id}, 
            "recursion_limit": 50
        }
        
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "original_query": query,
            "is_legal_query": True,
            "refined_query": "",
            "tools_to_run": [],
            "tool_responses": {},
            "all_sources": [],
            "final_response": "",
            "citation_mapping": {},
            "step_times": {}
        }
        
        # Execute workflow
        result = await self.graph.ainvoke(initial_state, config=config)
        
        workflow_time = time.time() - workflow_start
        logger.info(f"🏁 OPTIMIZED WORKFLOW COMPLETE - Total: {workflow_time:.2f}s")
        
        return result


@router.post("/agent")
async def optimized_legal_agent_chat(
    query: str = Query(..., description="The legal query to process"),
    current_user: CurrentUser = Depends(require(llm=True, qdrant=True))
):
    """
    Optimized 3-step legal research agent with Gemini-first approach
    
    Features:
    - Smart query analysis
    - Parallel tool execution  
    - Intelligent synthesis
    - No hardcoded keywords
    - Full async/await
    - Proper citation handling with id_ preservation
    """
    request_start = time.time()
    request_timestamp = get_current_timestamp()

    logger.info("📡 OPTIMIZED AGENT API START")
    logger.info(f"🕐 TIMESTAMP: {request_timestamp}")
    logger.info(f"👤 USER: {current_user.user.id}")
    logger.info(f"❓ QUERY: {query}")

    try:
        # Decode query
        decoded_query = urllib.parse.unquote(query)
        
        # Create and run optimized agent
        agent = OptimizedLegalAgent(current_user)
        result = await agent.run(decoded_query, str(current_user.user.id))

        # Process sources for response
        request_time = time.time() - request_start
        
        # FIXED: Extract sources by tool type while preserving id_
        all_sources = result.get("all_sources", [])
        najir_sources = [s for s in all_sources if s.get("tool") == "najir_search"]
        act_sources = [s for s in all_sources if s.get("tool") == "act_search"]
        summary_sources = [s for s in all_sources if s.get("tool") == "najir_summary"]
        constitution_sources = [s for s in all_sources if s.get("tool") == "constitution_search"]

        # FIXED: Process najir sources while preserving id_ and score
        najir_sources_for_processing = []
        for s in najir_sources:
            source_dict = {
                "id_": s.get("id_"),  # Preserve id_
                "text": s["text"], 
                "metadata": s["metadata"],
                "score": s.get("score", 0.0)  # Add missing score field
            }
            najir_sources_for_processing.append(source_dict)
        logger.info(f"✅ Processed Najir sources: {len(najir_sources_for_processing)}")
        processed_najir = await _process_sources(najir_sources_for_processing, current_user)
        logger.info(f"✅ Processed Najir sources: {len(processed_najir)}")
        # FIXED: Process citation mapping and validate response
        citation_mapping = result.get("citation_mapping", {})
        final_response = result.get("final_response", "")
        
        # Validate citations in response match citation_mapping
        if citation_mapping and final_response:
            # Find all citations in response
            citations_in_response = set(re.findall(r'\[(\d+)\]', final_response))
            available_citations = set(citation_mapping.keys())
            invalid_citations = citations_in_response - available_citations
            
            if invalid_citations:
                logger.warning(f"⚠️ FOUND INVALID CITATIONS IN RESPONSE: {invalid_citations}")
                # Remove invalid citations
                for invalid_cite in invalid_citations:
                    final_response = re.sub(rf'\[{invalid_cite}\]', '', final_response)
                final_response = re.sub(r'\s+', ' ', final_response).strip()
                logger.info(f"✅ CLEANED RESPONSE - Removed invalid citations")

        processed_citation_mapping = await process_citation(citation_mapping, current_user)
        
        response_data = {
            "response": final_response,
            "citation_mapping": processed_citation_mapping,
            "najir_sources": processed_najir.get("sources", []),
            "act_sources": [{"id_": s.get("id_"), "text": s["text"], "metadata": s["metadata"]} for s in act_sources],
            "najir_summary_sources": [{"id_": s.get("id_"), "text": s["text"], "metadata": s["metadata"]} for s in summary_sources],
            "constitution_sources": [{"id_": s.get("id_"), "text": s["text"], "metadata": s["metadata"]} for s in constitution_sources],
            "performance": {
                "total_time": request_time,
                "step_times": result.get("step_times", {}),
                "sources_count": len(all_sources)
            }
        }

        logger.info(f"✅ OPTIMIZED AGENT SUCCESS - {request_time:.2f}s, {len(all_sources)} sources")
        return response_data

    except Exception as e:
        request_time = time.time() - request_start
        logger.error(f"❌ OPTIMIZED AGENT ERROR - {request_time:.2f}s: {e}")

        return {
            "response": f"Error processing your request: {str(e)}",
            "citation_mapping": {},
            "najir_sources": [],
            "act_sources": [],
            "najir_summary_sources": [],
            "constitution_sources": [],
            "performance": {"total_time": request_time, "error": str(e)}
        }