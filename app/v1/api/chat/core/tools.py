from re import I
from langchain_core.tools import StructuredTool
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.postprocessor import LLMRerank
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
import json
import time
from openai import OpenAI
from google import genai
logger = StructuredLogger(__name__)

class LegalTools:
    def __init__(self):
        logger.info("🔧 LEGAL TOOLS INIT")
        self.rerank = None
        
    def _setup_rerank(self, llm):
        """Initialize rerank with user's LLM"""
        if not self.rerank:
            self.rerank = LLMRerank(llm=llm, top_n=15)
            logger.info("✅ RERANK CONFIGURED - top_n=15")
    
    def create_tools(self, current_user: CurrentUser):
        """Create legal research tools"""
        logger.info(f"🛠️ CREATING TOOLS - User: {current_user.user.id}")
        
        llm = current_user.llm.openai
        self._setup_rerank(llm)

        async def najir_search(query: str) -> str:
            """Search Nepali Supreme Court judgments and case law precedents"""
            start_time = time.time()
            logger.info(f"🏛️ NAJIR SEARCH: {query}")

            try:
                # Check if Qdrant index is available
                result = await current_user.qdrant.najir_retriever.aquery(query)
                logger.info(f"✅ NAJIR ENGINE COMPLETED for {query}")
                
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'najir',
                        'search_query': query
                    }
                } for node in result.source_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ NAJIR COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ NAJIR FAILED - {duration:.2f}s: {e}")
                return json.dumps({"response": f"Najir search error: {str(e)}", "sources": []})
        
        async def act_search(query: str) -> str:
            """Search Nepali Acts, statutes, laws, and regulations"""
            start_time = time.time()
            logger.info(f"📜 ACT SEARCH: {query}")

            try:
                # Check if Qdrant index is available
                result = await current_user.qdrant.act_retriever.aquery(query)
                logger.info(f"✅ ACT ENGINE COMPLETED for {query}")
                
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'act',
                        'search_query': query
                    }
                } for node in result.source_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ ACT COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ ACT FAILED - {duration:.2f}s: {e}")
                return json.dumps({"response": f"Error: {str(e)}", "sources": []})

        async def najir_summary(query: str) -> str:
            """Get concise summaries and key points from Nepali case law"""
            start_time = time.time()
            logger.info(f"📋 SUMMARY SEARCH: {query}")

            try:
                # Check if Qdrant index is available
                result = await current_user.qdrant.najir_summary_retriever.aquery(query)
                logger.info(f"✅ SUMMARY ENGINE COMPLETED for {query}")
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'najir_summary',
                        'search_query': query
                    }
                } for node in result.source_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ SUMMARY COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ SUMMARY FAILED - {duration:.2f}s: {e}")
                return json.dumps({"response": f"Error: {str(e)}", "sources": []})
        
        async def constitution_search(query: str) -> str:
            """Search the Constitution of Nepal - find constitutional provisions, articles, and amendments"""
            start_time = time.time()
            logger.info(f"📜 NEPAL CONSTITUTION SEARCH: {query}")

            try:
                # Check if Qdrant index is available
                result = await current_user.qdrant.constitution_retriever.aquery(query)
                logger.info(f"✅ CONSTITUTION ENGINE COMPLETED for {query}")
                
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'constitution',
                        'search_query': query
                    }
                } for node in result.source_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ NEPAL CONSTITUTION COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ NEPAL CONSTITUTION FAILED - {duration:.2f}s: {e}")
                return json.dumps({"response": f"Error: {str(e)}", "sources": []})


        tools = [
            StructuredTool.from_function(
                najir_search,
                name="najir_search",
                description="Search Nepali Supreme Court judgments, case law, and legal precedents - use for court decisions, rulings, and case analysis",
                coroutine=najir_search
            ),
            StructuredTool.from_function(
                act_search,
                name="act_search", 
                description="Search Nepali Acts, statutes, laws, and regulations - use for legislative provisions, legal codes, and statutory law",
                coroutine=act_search
            ),
            StructuredTool.from_function(
                najir_summary,
                name="najir_summary",
                description="Get concise summaries and key points from Nepali case law - use for quick understanding of court decisions",
                coroutine=najir_summary
            ),
            StructuredTool.from_function(
                constitution_search,
                name="constitution_search",
                description="Search Constitution of Nepal 2015 - find constitutional articles, fundamental rights, and constitutional provisions",
                coroutine=constitution_search
            )
        ]
        
        logger.info(f"✅ TOOLS CREATED - {len(tools)} tools ready")
        return tools