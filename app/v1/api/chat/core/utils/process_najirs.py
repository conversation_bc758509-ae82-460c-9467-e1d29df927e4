import asyncio
from typing import Dict, Any, List
from collections import defaultdict
from bson import ObjectId
from datetime import datetime
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
logger = StructuredLogger(__name__)



async def _process_sources(source_nodes, current_user: CurrentUser) -> Dict[str, Any]:
    """
    Common function to process and group sources like /retrieve route
    Preserves citation numbering from CitationQueryEngine
    Optimized for async processing with concurrent DB operations
    """
    
    if not source_nodes:
        return {"sources": [], "citation_mapping": {}}
    
    # Create mapping between citation numbers and source nodes
    citation_mapping = {}
    
    # Group nodes by article_mongo_id like in /retrieve route
    articles_dict = defaultdict(list)
    
    # Batch process chunk text fetching with concurrent DB calls
    async def fetch_chunk_text(node_data):
        """Fetch chunk text for a single node with all required data"""
        node, citation_number = node_data
        logger.info(f"Fetching chunk text for node {type(node)}")
        print(node)
        
        # Store original node_id before conversion
        original_node_id = None
        if isinstance(node, dict):
            original_node_id = node.get('id_', '')
            from llama_index.core.schema import TextNode, NodeWithScore
            node_obj = TextNode(
                id_=node.get('id_', ''),
                text=node.get('text', ''),
                metadata=node.get('metadata', {}),
                score=node.get('score', 0.0)
            )
            node = NodeWithScore(node=node_obj, score=node.get('score', 0.0))
        else:
            # For NodeWithScore objects, get id from the node attribute
            original_node_id = getattr(node.node, 'id_', '') if hasattr(node, 'node') else ''
        
        metadata = node.metadata if hasattr(node, 'metadata') else {}
        article_id = metadata.get('article_mongo_id', 'unknown')
        chunk_id = metadata.get('chunk_id', 'unknown')
        
        # Skip DB call for unknown articles
        if article_id == 'unknown' or chunk_id == 'unknown':
            return original_node_id, citation_number, article_id, chunk_id, node.text, metadata, node
        
        try:
            chunk_doc = await current_user.db.read_db.split_articles.find_one(
                {"metadata.article_mongo_id": article_id, "metadata.chunk_id": chunk_id},
                {"text": 1, "_id": 0}
            )
            chunk_text = chunk_doc.get('text', node.text) if chunk_doc else node.text
        except Exception as e:
            logger.log_exception_with_context(
                f"Error fetching chunk text for article {article_id}, chunk {chunk_id}: {str(e)}",
                {'article_id': article_id, 'chunk_id': chunk_id, 'exception_type': type(e).__name__, 'exception_details': str(e)}
            )
            chunk_text = node.text
            
        return original_node_id, citation_number, article_id, chunk_id, chunk_text, metadata, node
    
    # Process all nodes concurrently
    chunk_tasks = [
        fetch_chunk_text((node, i + 1)) 
        for i, node in enumerate(source_nodes)
    ]
    
    chunk_results = await asyncio.gather(*chunk_tasks, return_exceptions=True)
    
    # Process results and build data structures
    for result in chunk_results:
        if isinstance(result, Exception):
            logger.log_exception_with_context(
                f"Error processing chunk during async processing: {str(result)}",
                {'exception_type': type(result).__name__, 'exception_details': str(result)}
            )
            continue
            
        id_,citation_number, article_id, chunk_id, chunk_text, metadata, node = result
        
        # Use citation number from CitationQueryEngine (1-based indexing)
        chunk_citation_id = f"[{citation_number}]"
        
        # Map citation number to source info
        citation_mapping[citation_number] = {
            "id_": id_,
            "article_id": article_id,
            "chunk_id": chunk_id,
            "text": chunk_text,
            "metadata": metadata
        }
        
        # Handle node attribute access safely
        node_id = getattr(node, 'node_id', None) or getattr(node, 'hash', None) or str(hash(str(node)))
        score = getattr(node, 'score', 0.0) or 0.0
        
        chunk_data = {
            "chunk_id": node_id,
            "citation_id": chunk_citation_id,
            "citation_number": citation_number,
            "score": round(float(score), 4),
            "text": chunk_text,
            "metadata": metadata or {}
        }
        
        articles_dict[article_id].append(chunk_data)
    
    # Sort chunks within each article by score (highest first)
    for article_id in articles_dict:
        articles_dict[article_id].sort(key=lambda x: x['score'], reverse=True)
    
    # Batch process article fetching with concurrent DB calls
    async def fetch_article_data(article_id, chunks):
        """Fetch complete article data for a single article"""
        if article_id == 'unknown':
            return None
        
        try:
            article_doc = await current_user.db.read_db.documents.find_one({"_id": ObjectId(article_id)})
            if not article_doc:
                logger.error(f"Article with ID {article_id} not found in database")
                return None
            
            # Efficient serialization of MongoDB documents
            def serialize_value(value):
                """Recursively serialize MongoDB types"""
                if isinstance(value, (ObjectId, datetime)):
                    return str(value)
                elif isinstance(value, dict):
                    return {k: serialize_value(v) for k, v in value.items()}
                elif isinstance(value, list):
                    return [serialize_value(item) for item in value]
                else:
                    return value
            
            article_metadata = {key: serialize_value(value) for key, value in article_doc.items()}
            
            # Collect all citation numbers from chunks in this article
            citation_numbers = [chunk['citation_number'] for chunk in chunks]
            citation_ids = [chunk['citation_id'] for chunk in chunks]
            
            return {
                "article_mongo_id": article_id,
                "citation_numbers": citation_numbers,
                "citation_ids": citation_ids,
                "metadata": article_metadata,
                "chunks": chunks
            }
            
        except Exception as e:
            logger.log_exception_with_context(
                f"Error fetching article {article_id} from documents collection: {str(e)}",
                {'article_id': article_id, 'exception_type': type(e).__name__, 'exception_details': str(e)}
            )
            return None
    
    # Process all articles concurrently
    article_tasks = [
        fetch_article_data(article_id, chunks)
        for article_id, chunks in articles_dict.items()
        if article_id != 'unknown'
    ]
    
    if article_tasks:
        article_results = await asyncio.gather(*article_tasks, return_exceptions=True)
        
        # Filter out None results and exceptions
        sources = [
            result for result in article_results 
            if result is not None and not isinstance(result, Exception)
        ]
    else:
        sources = []
    
    # Sort articles by best chunk score
    if sources:
        sources.sort(key=lambda x: max(chunk['score'] for chunk in x['chunks']), reverse=True)
    
    return {
        "sources": sources,
        "citation_mapping": citation_mapping
    }

