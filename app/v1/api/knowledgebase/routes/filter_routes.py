from fastapi import APIRouter, Depends, HTTPException
from app.core.security import require
from app.models.current_user import CurrentUser
from app.v1.api.knowledgebase.models import FilterOptions
from app.v1.api.knowledgebase.core import get_filter_options_data

router = APIRouter()


@router.get("/filter", response_model=FilterOptions)
async def get_filter_options(
    current_user: CurrentUser = Depends(require())  # Database only - no LLM/Qdrant needed
):
    """Get available filter options for frontend"""
    try:
        collection = current_user.db.read_db.documents
        return await get_filter_options_data(collection)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting filter options: {str(e)}")
