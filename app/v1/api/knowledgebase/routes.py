"""
Knowledge base routes - clean and simple
"""

from fastapi import APIRouter, Depends, HTTPException
from app.core.security import get_tenant_info
from app.models.current_user import CurrentUser
from app.core.utils.response import PaginatedResponse
from .models import FilterOptions, FindRequest
from .core import get_filter_options_data, find_documents_data

router = APIRouter(tags=["Knowledge Base"], prefix="/knowledgebase")


@router.get("/filter", response_model=FilterOptions)
async def get_filter_options(
    current_user: CurrentUser = Depends(dependency=get_tenant_info)
):
    """Get available filter options for frontend"""
    try:
        collection = current_user.db.read_db.documents
        return await get_filter_options_data(collection)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting filter options: {str(e)}")


# @router.post("/find", response_model=PaginatedResponse)
# async def find_knowledgebase(
#     find_request: FindRequest,
#     current_user: CurrentUser = Depends(dependency=get_tenant_info)
# ):
#     """Find knowledgebase using POST with filters from GET /filter response"""
#     try:
#         collection = current_user.db.read_db.documents
#         return await find_documents_data(collection, find_request)
#     except Exception as e:
#         raise HTTPException(status_code=400, detail=f"Error: {str(e)}")
