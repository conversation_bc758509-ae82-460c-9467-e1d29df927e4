from typing import Any, Optional
from pydantic import BaseModel
from app.models.user import User
from pymongo import MongoClient, AsyncMongoClient
from typing import Dict

from app.core.llm_client import create_llm_clients
from app.core.qdrant import create_qdrant_client

from app.core.logger import StructuredLogger
logger = StructuredLogger(__name__)

class CurrentUserDB(BaseModel):
    """Organized database access for current user"""
    client: MongoClient  # MongoDB client
    async_client: AsyncMongoClient  # Async MongoDB client
    read_db: Any  # Read database
    write_db: Optional[Any] = None  # Write database (admin only)
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUserQdrant(BaseModel):
    """Organized Qdrant access for current user
    
    All fields are optional as they will be initialized lazily when needed.
    """
    qdrant: Optional[Any] = None  # Async Qdrant client
    qdrant_sync: Optional[Any] = None  # Sync Qdrant client
    najir_retriever: Optional[Any] = None  # Najir retriever
    act_retriever: Optional[Any] = None  # Act retriever
    najir_summary_retriever: Optional[Any] = None  # Summary retriever
    constitution_retriever: Optional[Any] = None  # Constitution retriever
    
    class Config:
        arbitrary_types_allowed = True
    
    @classmethod
    async def create(cls, config_db: Any, llm) -> "CurrentUserQdrant":
        """Create CurrentUserQdrant with initialized clients from database config"""
        result = await create_qdrant_client(config_db, llm)
        
        if result is None or len(result) != 6:
            logger.error("Failed to create Qdrant client or invalid return format")
            return cls()
            
        (qdrant_async, qdrant_sync, 
         najir_retriever, act_retriever, 
         najir_summary_retriever, constitution_retriever) = result
        
        return cls(
            qdrant=qdrant_async,
            qdrant_sync=qdrant_sync,
            najir_retriever=najir_retriever,
            act_retriever=act_retriever,
            najir_summary_retriever=najir_summary_retriever,
            constitution_retriever=constitution_retriever
        )


class CurrentUserLLM(BaseModel):
    """Organized LLM access for current user
    
    All fields are optional as they will be initialized lazily when needed.
    """
    openai: Optional[Any] = None  # OpenAI LLM client
    gemini: Optional[Any] = None  # Gemini LLM client
    apikeys: Optional[Dict[str, str]] = {}  # API keys
    
    class Config:
        arbitrary_types_allowed = True
    
    @classmethod
    async def create(cls, config_db: Any) -> "CurrentUserLLM":
        """Create CurrentUserLLM with initialized clients from database config"""
        llm_clients, apikeys = await create_llm_clients(config_db)
        return cls(
            openai=llm_clients.openai,
            gemini=llm_clients.gemini,
            apikeys=apikeys
        )


class CurrentUser(BaseModel):
    """
    Organized current user structure with clean access patterns:
    - currentuser.user: User information
    - currentuser.db: Database access (client, read_db, write_db)
    - currentuser.qdrant: All Qdrant indexes and clients
    - currentuser.llm: LLM clients and managers
    """
    user: User  # User information
    tenant_id: str
    tenant_name: str
    db: CurrentUserDB  # Database access
    qdrant: Optional[CurrentUserQdrant] = None  # Qdrant clients and indices
    llm: Optional[CurrentUserLLM] = None  # LLM clients
    
    class Config:
        arbitrary_types_allowed = True
        underscore_attrs_are_private = True
    

        
    def has_write_access(self) -> bool:
        """Check if user has write database access"""
        return self.db.write_db is not None
        
    def get_db_for_operation(self, operation_type: str = "read") -> Any:
        """Get appropriate database based on operation type"""
        if operation_type.lower() == "write":
            if self.db.write_db is None:
                from fastapi import HTTPException
                raise HTTPException(
                    status_code=403,
                    detail="Write access denied. Admin role required."
                )
            return self.db.write_db
        return self.db.read_db
