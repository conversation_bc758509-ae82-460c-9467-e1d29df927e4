# src/core/security.py

import asyncio
from datetime import datetime, timedelta
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
# from passlib.context import CryptContext
from fastapi.security import OAuth2<PERSON>assword<PERSON>earer
from fastapi import Depends, HTTPException
# Initialize Qdrant and LLM clients
from app.core.qdrant import create_qdrant_client
from app.core.llm_client import create_llm_clients
import jwt
from app.core.config import SECRET_KEY, ALGORITHM
from app.models.user import User
from app.models.current_user import CurrentUser, CurrentUserDB, CurrentUserQdrant, CurrentUserLLM
from app.models.permission import Permission
from app.models.role import Role
from app.core.database import get_user_databases_async, get_db_from_tenant_id
from typing import Optional, List, Callable, Any
from types import CoroutineType
from app.core.logger import StructuredLogger

logger = StructuredLogger(__name__)

ph = PasswordHasher()
# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="login",
    scheme_name="Bearer Token",
    description="Use credentials: username=admin, password=password, client_id=client_id",
    auto_error=False,  # Don't automatically return 401, let us handle it
)

def create_access_token(data: dict, expires_delta:timedelta = timedelta(days=1)) -> str:
    to_encode = data.copy() 
    if expires_delta:   
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_tenant_info(
    init_qdrant: bool = False,
    init_llm: bool = False,
    token: str = Depends(oauth2_scheme)
) -> CurrentUser:
    """Simple user authentication with optional service initialization"""
    try:
        # Decode JWT token
        payload = jwt.decode(jwt=token, key=SECRET_KEY, algorithms=[ALGORITHM])
        username = payload.get("sub")
        tenant_id = payload.get("tenant_id")

        if not username or not tenant_id:
            raise HTTPException(status_code=401, detail="Invalid token")

        # Get user databases
        user_dbs = await get_user_databases_async(tenant_id=tenant_id)
        read_db = user_dbs["read_db"]

        # Find user
        user_doc = await read_db.users.find_one({"username": username})
        if not user_doc:
            raise HTTPException(status_code=401, detail="User not found")

        # Get permissions
        user_permissions = []
        for perm_name in user_doc.get("permissions", []):
            perm_doc = await read_db.permissions.find_one({"name": perm_name})
            if perm_doc:
                user_permissions.append(Permission(**perm_doc))
        user_doc["permissions"] = user_permissions

        # Create user object
        user = User(**user_doc)
        user_dbs_with_roles = await get_user_databases_async(tenant_id=tenant_id, user_role=user.role)
        llm=await CurrentUserLLM.create(user_dbs_with_roles["read_db"]) if init_llm else None

        # Create CurrentUser - complete and clean
        return CurrentUser(
            user=user,
            tenant_id=tenant_id,
            tenant_name=user_dbs_with_roles["tenant_name"],
            db=CurrentUserDB(
                client=user_dbs_with_roles["client"],
                async_client=user_dbs_with_roles["async_client"],
                read_db=user_dbs_with_roles["read_db"],
                write_db=user_dbs_with_roles["write_db"]
            ),
            llm=llm,
            qdrant=await CurrentUserQdrant.create(user_dbs_with_roles["read_db"],llm) if init_qdrant else None
        )
        
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
    except Exception as e:
        logger.error(f"Error in get_tenant_info: {str(e)}")
        raise HTTPException(status_code=500, detail="Authentication failed")

# Clean dependency functions
def require(roles: List[str] = None, db: bool = True, llm: bool = False, qdrant: bool = False):
    """
    Universal dependency function for authentication and service initialization

    Args:
        roles: List of required roles (optional, if None then any authenticated user)
        db: Whether to initialize database access (default: True)
        llm: Whether to initialize LLM clients (default: False)
        qdrant: Whether to initialize Qdrant clients (default: False)

    Usage:
        # Database only (fastest, default)
        current_user: CurrentUser = Depends(require())
        current_user: CurrentUser = Depends(require(db=True))

        # With roles
        current_user: CurrentUser = Depends(require(roles=["admin", "lawyer"]))

        # With LLM only
        current_user: CurrentUser = Depends(require(llm=True))

        # With Qdrant only
        current_user: CurrentUser = Depends(require(qdrant=True))

        # With both LLM and Qdrant
        current_user: CurrentUser = Depends(require(llm=True, qdrant=True))

        # With roles and services
        current_user: CurrentUser = Depends(require(roles=["admin"], llm=True, qdrant=True))

        # Explicit combinations for clarity
        current_user: CurrentUser = Depends(require(db=True, llm=True))
        current_user: CurrentUser = Depends(require(db=True, qdrant=True))
        current_user: CurrentUser = Depends(require(db=True, llm=True, qdrant=True))
    """
    async def dependency(token: str = Depends(oauth2_scheme)) -> CurrentUser:
        # Get user with requested services (db is always True by default)
        # The get_tenant_info function returns a coroutine, so we need to await it
        get_user_coro = get_tenant_info(init_llm=llm, init_qdrant=qdrant, token=token)
        current_user = await get_user_coro

        # Check roles if specified
        if roles and not current_user.user.has_any_role(roles):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(roles)}"
            )

        return current_user

    return dependency

def require_roles(required_roles: List[str], init_llm: bool = False, init_qdrant: bool = False):
    """
    Dependency to check if user has any of the required roles with optimized service initialization

    Args:
        required_roles: List of roles that are allowed to access the endpoint
        init_llm: Whether to initialize LLM clients (default: False)
        init_qdrant: Whether to initialize Qdrant clients (default: False)

    Usage:
        # Database only (fastest)
        current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))

        # With LLM
        current_user: CurrentUser = Depends(require_roles(["admin"], init_llm=True))

        # With both LLM and Qdrant
        current_user: CurrentUser = Depends(require_roles(["admin"], init_llm=True, init_qdrant=True))
    """
    async def check_roles(current_user: CurrentUser = Depends(
        lambda token=Depends(oauth2_scheme): get_tenant_info(init_llm=init_llm, init_qdrant=init_qdrant, token=token)
    )):
        if not current_user.user.has_any_role(required_roles):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(required_roles)}"
            )
        return current_user
    return check_roles

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


# Replace CryptContext with PasswordHasher
def hash_password(password: str) -> str:
    """
    Hash new passwords using Argon2
    """
    return ph.hash(password)
    
# Update the verify_password function
def verify_password(plain_password, hashed_password) -> bool:
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return ph.verify(hash=hashed_password, password=plain_password)
    except VerifyMismatchError:
        return False


def require_permissions(required_permissions: list[str]) :
    """
    Dependency that checks if the user has all the required permissions.
    Usage: @router.get("/endpoint", dependencies=[Depends(require_permissions(["read:users", "write:users"]))])
    """
    async def check_permissions(current_user: CurrentUser = Depends(dependency=get_tenant_info)) -> CurrentUser:
        user_permissions = [p.name for p in current_user.user.permissions]
        
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    return check_permissions


