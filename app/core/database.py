from pymongo import MongoClient, AsyncMongoClient, ReadPreference, WriteConcern
from dotenv import load_dotenv
from bson import ObjectId
import os
from fastapi import HTTPException
from typing import Optional, Any, TypedDict
from app.core.performance import <PERSON><PERSON><PERSON>, CacheManager, async_timed

load_dotenv()

# Type definitions
class DatabaseConfig(TypedDict):
    read_db: Any
    write_db: Optional[Any]
    client: Any
    async_client: Any
    tenant_name: str
    tenant_id: str

def _get_mongo_uri() -> str:
    mongo_uri = os.getenv("MONGO_URI")
    if not mongo_uri:
        raise HTTPException(status_code=500, detail="MONGO_URI not set")
    return mongo_uri

def _get_project_name() -> str:
    project_name = os.getenv("PROJECT_NAME")
    if not project_name:
        raise HTTPException(status_code=500, detail="PROJECT_NAME not set")
    return project_name.lower()

def get_read_db(db_name: str):
    """Get read database - optimized with connection pooling"""
    client = ConnectionPool.get_async_mongo_client(_get_mongo_uri(), ReadPreference.SECONDARY_PREFERRED)
    return client[db_name]

def get_write_db(db_name: str):
    """Get write database - optimized with connection pooling"""
    client = ConnectionPool.get_async_mongo_client(_get_mongo_uri(), ReadPreference.PRIMARY)
    db = client[db_name]
    # Apply write concern at database level
    return db.with_options(write_concern=WriteConcern(w="majority"))

@async_timed
async def get_user_databases_async(tenant_id: str, user_role: str = None) -> DatabaseConfig:
    """Get user databases - optimized with caching and connection pooling"""

    # Try to get tenant info from cache first
    cached_tenant = CacheManager.get_tenant_cache(tenant_id)
    if cached_tenant:
        tenant_doc = cached_tenant
    else:
        # Get tenant info from database
        admin_db_name = f"{_get_project_name()}_admin"
        admin_db = get_read_db(admin_db_name)

        tenant_doc = await admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
        if not tenant_doc:
            raise HTTPException(status_code=404, detail="Tenant not found")

        # Cache tenant info for future requests
        CacheManager.set_tenant_cache(tenant_id, tenant_doc)

    tenant_database_name = tenant_doc["db_name"]

    # Read database for all users (optimized with connection pooling)
    read_db = get_read_db(tenant_database_name)

    # Use optimized connection pool
    client = ConnectionPool.get_mongo_client(_get_mongo_uri(), ReadPreference.SECONDARY_PREFERRED)
    async_client = ConnectionPool.get_async_mongo_client(_get_mongo_uri(), ReadPreference.PRIMARY)

    # Write database only for admin role
    write_db = None
    if user_role == "admin":
        write_db = get_write_db(tenant_database_name)

    return DatabaseConfig(
        read_db=read_db,
        write_db=write_db,
        client=client,
        async_client=async_client,
        tenant_name=tenant_doc.get("name", "Unknown"),
        tenant_id=tenant_id
    )

async def get_db_from_tenant_id_async(tenant_id: str, user_roles: Optional[list] = None):
    """Legacy function - returns read DB"""
    user_dbs = await get_user_databases_async(tenant_id, user_roles)
    return user_dbs["read_db"]

async def get_tenant_id_and_name_from_slug_async(slug: str):
    """Get tenant info from slug"""
    admin_db_name = f"{_get_project_name()}_admin"
    admin_db = get_read_db(admin_db_name)
    
    tenant_doc = await admin_db.tenants.find_one({"slug": slug}, {"_id": 1, "name": 1})
    if not tenant_doc:
        raise HTTPException(status_code=404, detail="Slug not found")
    return tenant_doc

# Legacy sync functions for backward compatibility
def get_admin_db():
    """Legacy sync function - use async versions instead"""
    from pymongo import MongoClient
    admin_db_name = f"{_get_project_name()}_admin"
    client = MongoClient(_get_mongo_uri())
    return client[admin_db_name]

def get_db_from_tenant_id(tenant_id: str):
    """Legacy sync function - use async versions instead"""
    from pymongo import MongoClient
    admin_db = get_admin_db()
    tenant_doc = admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant_doc:
        raise HTTPException(status_code=404, detail="Tenant not found")
    client = MongoClient(_get_mongo_uri())
    return client[tenant_doc["db_name"]]

def get_tenant_id_and_name_from_slug(slug: str):
    """Legacy sync function - use async versions instead"""
    admin_db = get_admin_db()
    tenant_doc = admin_db.tenants.find_one({"slug": slug}, {"_id": 1, "name": 1})
    if not tenant_doc:
        raise HTTPException(status_code=404, detail="Slug not found")
    return tenant_doc

# Setup functions
async def setup_admin_database():
    """Setup admin database at startup"""
    admin_db_name = f"{_get_project_name()}_admin"
    admin_db = get_write_db(admin_db_name)

    collections = await admin_db.list_collection_names()
    if "tenants" not in collections:
        await admin_db.create_collection("tenants")
        await admin_db.tenants.create_index("slug", unique=True)
        await admin_db.tenants.create_index("db_name", unique=True)

    return True

async def setup_tenant_database(tenant_id: str):
    """Setup tenant database with dummy users and roles"""
    from app.core.security import hash_password

    # Get tenant info
    admin_db_name = f"{_get_project_name()}_admin"
    admin_db = get_read_db(admin_db_name)

    tenant_doc = await admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
    if not tenant_doc:
        raise HTTPException(status_code=404, detail="Tenant not found")

    tenant_db_name = tenant_doc["db_name"]
    tenant_db = get_write_db(tenant_db_name)

    # Create collections
    collections = await tenant_db.list_collection_names()
    required_collections = ["users", "roles", "permissions", "config"]

    for collection_name in required_collections:
        if collection_name not in collections:
            await tenant_db.create_collection(collection_name)

    # Create indexes
    await tenant_db.users.create_index("username", unique=True)
    await tenant_db.roles.create_index("name", unique=True)
    await tenant_db.permissions.create_index("name", unique=True)

    # Create default permissions
    permissions = [
        {"name": "read_users", "description": "Read users"},
        {"name": "write_users", "description": "Write users"},
        {"name": "read_data", "description": "Read data"},
        {"name": "write_data", "description": "Write data"},
        {"name": "admin_access", "description": "Admin access"}
    ]

    for perm in permissions:
        await tenant_db.permissions.update_one(
            {"name": perm["name"]}, {"$set": perm}, upsert=True
        )

    # Create default roles
    roles = [
        {"name": "admin", "permissions": ["read_users", "write_users", "read_data", "write_data", "admin_access"]},
        {"name": "lawyer", "permissions": ["read_users", "write_users", "read_data", "write_data", "admin_access"]},
        {"name": "user", "permissions": ["read_data"]}
    ]

    for role in roles:
        await tenant_db.roles.update_one(
            {"name": role["name"]}, {"$set": role}, upsert=True
        )

    # Create dummy users
    users = [
        {"username": "admin", "password": "admin123", "role": "admin"},
        {"username": "lawyer", "password": "lawyer123", "role": "lawyer"},
        {"username": "user", "password": "user123", "role": "user"}
    ]

    for user in users:
        user_doc = {
            "username": user["username"],
            "hashed_password": hash_password(user["password"]),
            "role": user["role"],
            "permissions": [],
            "created_at": ObjectId().generation_time,
            "status": "active"
        }

        # Get permissions for role
        role_doc = await tenant_db.roles.find_one({"name": user["role"]})
        if role_doc:
            user_doc["permissions"] = role_doc["permissions"]

        await tenant_db.users.update_one(
            {"username": user["username"]}, {"$set": user_doc}, upsert=True
        )

    # Update tenant setup flag
    admin_write_db = get_write_db(admin_db_name)
    await admin_write_db.tenants.update_one(
        {"_id": ObjectId(tenant_id)},
        {"$set": {"is_setup": True, "setup_date": ObjectId().generation_time}}
    )

    return True

async def check_tenant_setup(tenant_id: str) -> bool:
    """Check if tenant is setup"""
    admin_db_name = f"{_get_project_name()}_admin"
    admin_db = get_read_db(admin_db_name)

    tenant_doc = await admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
    return tenant_doc.get("is_setup", False) if tenant_doc else False
