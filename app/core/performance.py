"""
Performance optimization utilities for the Legal Backend API
Includes connection pooling, caching, and async optimizations
"""

import asyncio
import time
from typing import Dict, Any, Optional
from functools import wraps, lru_cache
from pymongo import MongoClient, AsyncMongoClient
from app.core.logger import StructuredLogger
import os

logger = StructuredLogger(__name__)

# Global connection pools
_mongo_client_pool: Dict[str, MongoClient] = {}
_async_mongo_client_pool: Dict[str, AsyncMongoClient] = {}
_qdrant_client_pool: Dict[str, Any] = {}

# Cache for frequently accessed data
_tenant_cache: Dict[str, Dict[str, Any]] = {}
_config_cache: Dict[str, Dict[str, Any]] = {}
_cache_timestamps: Dict[str, float] = {}

# Cache TTL in seconds
CACHE_TTL = 300  # 5 minutes


class ConnectionPool:
    """
    Optimized connection pool manager for MongoDB and Qdrant
    """
    
    @staticmethod
    def get_mongo_client(uri: str, read_preference=None) -> MongoClient:
        """Get or create MongoDB client with connection pooling"""
        cache_key = f"{uri}_{read_preference}"

        if cache_key not in _mongo_client_pool:
            logger.debug(f"Creating new MongoDB client for {cache_key}")
            client_kwargs = {
                "maxPoolSize": 50,
                "minPoolSize": 5,
                "maxIdleTimeMS": 30000,
                "waitQueueTimeoutMS": 5000,
                "serverSelectionTimeoutMS": 5000,
                "connectTimeoutMS": 10000,
                "socketTimeoutMS": 20000,
                "retryWrites": True,
                "retryReads": True,
                "compressors": "snappy,zlib",
            }

            # Only add read_preference if it's not None
            if read_preference is not None:
                client_kwargs["read_preference"] = read_preference

            _mongo_client_pool[cache_key] = MongoClient(uri, **client_kwargs)

        
        return _mongo_client_pool[cache_key]
    
    @staticmethod
    def get_async_mongo_client(uri: str, read_preference=None) -> AsyncMongoClient:
        """Get or create async MongoDB client with connection pooling"""
        cache_key = f"async_{uri}_{read_preference}"

        if cache_key not in _async_mongo_client_pool:
            logger.debug(f"Creating new async MongoDB client for {cache_key}")
            client_kwargs = {
                "maxPoolSize": 50,
                "minPoolSize": 5,
                "maxIdleTimeMS": 30000,
                "waitQueueTimeoutMS": 5000,
                "serverSelectionTimeoutMS": 5000,
                "connectTimeoutMS": 10000,
                "socketTimeoutMS": 20000,
                "retryWrites": True,
                "retryReads": True,
                "compressors": "snappy,zlib",
            }

            # Only add read_preference if it's not None
            if read_preference is not None:
                client_kwargs["read_preference"] = read_preference

            _async_mongo_client_pool[cache_key] = AsyncMongoClient(uri, **client_kwargs)
        
        return _async_mongo_client_pool[cache_key]


class CacheManager:
    """
    Simple in-memory cache with TTL for frequently accessed data
    """
    
    @staticmethod
    def _is_cache_valid(cache_key: str) -> bool:
        """Check if cache entry is still valid"""
        if cache_key not in _cache_timestamps:
            return False
        
        return time.time() - _cache_timestamps[cache_key] < CACHE_TTL
    
    @staticmethod
    def get_tenant_cache(tenant_id: str) -> Optional[Dict[str, Any]]:
        """Get cached tenant data"""
        cache_key = f"tenant_{tenant_id}"
        
        if cache_key in _tenant_cache and CacheManager._is_cache_valid(cache_key):
            logger.debug(f"Cache hit for tenant {tenant_id}")
            return _tenant_cache[cache_key]
        
        return None
    
    @staticmethod
    def set_tenant_cache(tenant_id: str, data: Dict[str, Any]):
        """Cache tenant data"""
        cache_key = f"tenant_{tenant_id}"
        _tenant_cache[cache_key] = data
        _cache_timestamps[cache_key] = time.time()
        logger.debug(f"Cached tenant data for {tenant_id}")
    
    @staticmethod
    def get_config_cache(config_name: str) -> Optional[Dict[str, Any]]:
        """Get cached config data"""
        cache_key = f"config_{config_name}"
        
        if cache_key in _config_cache and CacheManager._is_cache_valid(cache_key):
            logger.debug(f"Cache hit for config {config_name}")
            return _config_cache[cache_key]
        
        return None
    
    @staticmethod
    def set_config_cache(config_name: str, data: Dict[str, Any]):
        """Cache config data"""
        cache_key = f"config_{config_name}"
        _config_cache[cache_key] = data
        _cache_timestamps[cache_key] = time.time()
        logger.debug(f"Cached config data for {config_name}")
    
    @staticmethod
    def clear_cache():
        """Clear all cached data"""
        _tenant_cache.clear()
        _config_cache.clear()
        _cache_timestamps.clear()
        logger.info("Cache cleared")


def async_timed(func):
    """Decorator to measure async function execution time"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            if execution_time > 1.0:  # Log slow operations
                logger.warning(f"Slow operation: {func.__name__} took {execution_time:.3f}s")
            else:
                logger.debug(f"Operation: {func.__name__} took {execution_time:.3f}s")
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Failed operation: {func.__name__} failed after {execution_time:.3f}s - {str(e)}")
            raise
    
    return wrapper


def batch_operation(batch_size: int = 100):
    """Decorator for batching database operations"""
    def decorator(func):
        @wraps(func)
        async def wrapper(items, *args, **kwargs):
            if not items:
                return []
            
            results = []
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                batch_result = await func(batch, *args, **kwargs)
                results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
            
            return results
        
        return wrapper
    return decorator


class DatabaseOptimizer:
    """
    Database optimization utilities
    """
    
    @staticmethod
    async def create_indexes_if_missing(db, collection_name: str, indexes: list):
        """Create database indexes if they don't exist"""
        try:
            collection = db[collection_name]
            existing_indexes = await collection.list_indexes().to_list(None)
            existing_index_names = {idx.get('name') for idx in existing_indexes}
            
            for index_spec in indexes:
                index_name = index_spec.get('name')
                if index_name not in existing_index_names:
                    await collection.create_index(
                        index_spec['keys'],
                        name=index_name,
                        **index_spec.get('options', {})
                    )
                    logger.info(f"Created index {index_name} on {collection_name}")
        
        except Exception as e:
            logger.error(f"Failed to create indexes for {collection_name}: {e}")
    
    @staticmethod
    async def optimize_collection(db, collection_name: str):
        """Apply optimization settings to a collection"""
        try:
            # Common indexes for performance
            common_indexes = [
                {
                    'name': 'created_at_1',
                    'keys': [('created_at', 1)],
                    'options': {}
                },
                {
                    'name': 'status_1',
                    'keys': [('status', 1)],
                    'options': {}
                }
            ]
            
            # Collection-specific indexes
            specific_indexes = {
                'users': [
                    {'name': 'username_1', 'keys': [('username', 1)], 'options': {'unique': True}},
                    {'name': 'email_1', 'keys': [('email', 1)], 'options': {'sparse': True}},
                    {'name': 'roles_1', 'keys': [('roles', 1)], 'options': {}}
                ],
                'tenants': [
                    {'name': 'slug_1', 'keys': [('slug', 1)], 'options': {'unique': True}},
                    {'name': 'db_name_1', 'keys': [('db_name', 1)], 'options': {'unique': True}}
                ],
                'documents': [
                    {'name': 'article_mongo_id_1', 'keys': [('article_mongo_id', 1)], 'options': {}},
                    {'name': 'text_index', 'keys': [('$**', 'text')], 'options': {}}
                ]
            }
            
            indexes_to_create = common_indexes + specific_indexes.get(collection_name, [])
            await DatabaseOptimizer.create_indexes_if_missing(db, collection_name, indexes_to_create)
            
        except Exception as e:
            logger.error(f"Failed to optimize collection {collection_name}: {e}")


@lru_cache(maxsize=128)
def get_cached_config_value(config_name: str, default_value: Any = None) -> Any:
    """Get configuration value with LRU caching"""
    return os.getenv(config_name, default_value)


async def warm_up_connections():
    """Warm up database connections at startup"""
    try:
        logger.info("Warming up database connections...")

        from app.core.database import _get_mongo_uri
        from pymongo import ReadPreference

        # Warm up MongoDB connections with proper read preferences
        uri = _get_mongo_uri()
        ConnectionPool.get_mongo_client(uri, ReadPreference.SECONDARY_PREFERRED)
        ConnectionPool.get_async_mongo_client(uri, ReadPreference.PRIMARY)

        logger.info("Database connections warmed up successfully")

    except Exception as e:
        logger.error(f"Failed to warm up connections: {e}")


async def cleanup_connections():
    """Clean up connections at shutdown"""
    try:
        logger.info("Cleaning up database connections...")
        
        # Close MongoDB connections
        for client in _mongo_client_pool.values():
            client.close()
        
        for client in _async_mongo_client_pool.values():
            client.close()
        
        # Clear pools
        _mongo_client_pool.clear()
        _async_mongo_client_pool.clear()
        _qdrant_client_pool.clear()
        
        # Clear caches
        CacheManager.clear_cache()
        
        logger.info("Connections cleaned up successfully")
        
    except Exception as e:
        logger.error(f"Failed to cleanup connections: {e}")
