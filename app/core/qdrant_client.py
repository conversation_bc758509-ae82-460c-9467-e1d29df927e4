# """
# Qdrant client for vector database operations
# """

# from app.core.logger import StructuredLogger
# from typing import Optional
# from qdrant_client import AsyncQdrantClient
# from llama_index.vector_stores.qdrant import QdrantVectorStore
# from llama_index.core import VectorStoreIndex
# from llama_index.core import StorageContext
# from llama_index.embeddings.jinaai import JinaEmbedding
# from llama_index.embeddings.openai import OpenAIEmbedding
# import asyncio
# import os
# logger = StructuredLogger(__name__)


# from qdrant_client import QdrantClient

# # Global cache for Qdrant clients and indices
# _qdrant_clients = {}
# _qdrant_indices = {}

# async def get_or_create_qdrant_clients(config_db, force_create=False):
#     """Get or create Qdrant clients with connection pooling and lazy loading"""
#     global _qdrant_clients
    
#     # Use a consistent key for the config
#     config_key = f"{id(config_db)}-{force_create}"
    
#     # Return cached clients if available and not forcing recreation
#     if config_key in _qdrant_clients and not force_create:
#         logger.debug("Returning cached Qdrant clients")
#         return _qdrant_clients[config_key]
    
#     try:
#         # Get Qdrant config from database
#         qdrant_config = await config_db.config.find_one({"name": "qdrant_config"})

#         if not qdrant_config:
#             logger.warning("Qdrant config not found in database")
#             return None, None

#         host = qdrant_config["host"]
#         port = qdrant_config["port"]
        
#         # Create async client with improved connection settings
#         async_client = AsyncQdrantClient(
#             host=host,
#             port=port,
#             prefer_grpc=False,  # Use REST API for better stability
#             https=False,
#         )
        
#         # Create sync client with improved connection settings
#         sync_client = QdrantClient(
#             host=host,
#             port=port,
#             prefer_grpc=False,  # Use REST API for better stability
#             https=False,
#         )
        
#         logger.info(f"Created new Qdrant clients for {host}:{port}")
        
#         # Cache the clients
#         _qdrant_clients[config_key] = (async_client, sync_client)
        
#         return async_client, sync_client
        
#     except Exception as e:
#         logger.error(f"Failed to create Qdrant clients: {e}")
#         return None, None

# async def create_qdrant_indices(async_client, sync_client, config_db, force_create=False):
#     """Create Qdrant indices with lazy loading"""
#     global _qdrant_indices
    
#     # Use a consistent key for the config
#     config_key = f"{id(config_db)}-{force_create}"
    
#     # Return cached indices if available and not forcing recreation
#     if config_key in _qdrant_indices and not force_create:
#         logger.debug("Returning cached Qdrant indices")
#         return _qdrant_indices[config_key]
    
#     try:
#         # Get Qdrant config from database
#         qdrant_config = await config_db.config.find_one({"name": "qdrant_config"})

#         if not qdrant_config:
#             logger.warning("Qdrant config not found in database")
#             return None, None, None, None, None, None

#         sent_coll_name = qdrant_config.get("search_coll", "legal_sentence_split")
#         act_coll_name = qdrant_config.get("legal_act_coll", "legal_acts_jina")
#         summary_coll_name = qdrant_config.get("najir_summary_col", "legal_summary_jina")
#         constitution_split = qdrant_config.get("constitution_split", "constitution_split")

#         # Create embed models (consider moving these to a separate function if used elsewhere)
#         embed_model = OpenAIEmbedding(
#             api_key=os.getenv("OPENAI_API_KEY"),
#             model="text-embedding-3-large",
#             embed_batch_size=10,
#             task="text-matching",
#             dimensions=2048
#         )
        
#         embed_model1 = OpenAIEmbedding(
#             api_key=os.getenv("OPENAI_API_KEY"),
#             model="text-embedding-3-large",
#             embed_batch_size=10,
#             task="text-matching",
#             dimensions=1536
#         )
        
#         # Create indices in parallel with error handling
#         logger.info("Creating Qdrant indices...")
#         results = await asyncio.gather(
#             _create_index(async_client, sync_client, sent_coll_name, embed_model1, "sentence"),
#             _create_index(async_client, sync_client, act_coll_name, embed_model, "act"),
#             _create_index(async_client, sync_client, summary_coll_name, embed_model, "summary"),
#             _create_index(async_client, sync_client, constitution_split, embed_model, "constitution"),
#             return_exceptions=True
#         )
        
#         # Unpack results with error handling
#         indices = {}
#         index_names = ["sentence", "act", "summary", "constitution"]
        
#         for i, result in enumerate(results):
#             index_name = index_names[i]
#             if isinstance(result, Exception):
#                 logger.error(f"Failed to create {index_name} index: {result}")
#                 indices[index_name] = (None, None)
#             elif result is None or result == (None, None):
#                 logger.warning(f"No {index_name} index created")
#                 indices[index_name] = (None, None)
#             else:
#                 logger.info(f"✅ Successfully created {index_name} index")
#                 indices[index_name] = result
        
#         # Extract individual indices with fallbacks
#         qd_index_async, qd_index_sync = indices.get("sentence", (None, None))
#         act_index, act_sync_index = indices.get("act", (None, None))
#         summary_index, async_summary_index = indices.get("summary", (None, None))
#         constitution_index, _ = indices.get("constitution", (None, None))
        
#         # Cache the indices (even if some are None)
#         _qdrant_indices[config_key] = (
#             qd_index_async, qd_index_sync, 
#             act_index, act_sync_index, 
#             summary_index, async_summary_index,
#             constitution_index
#         )
        
#         # Log summary of successful indices
#         successful_indices = [name for name, (async_idx, sync_idx) in indices.items() if async_idx is not None]
#         logger.info(f"✅ Successfully created {len(successful_indices)}/{len(index_names)} Qdrant indices: {successful_indices}")
        
#         return (
#             qd_index_async, qd_index_sync, 
#             act_index, act_sync_index, 
#             summary_index, async_summary_index,
#             constitution_index
#         )
        
#     except Exception as e:
#         logger.error(f"Failed to create Qdrant indices: {e}")
#         return (None, None, None, None, None, None, None)

# async def _create_index(async_client, sync_client, collection_name, embed_model, index_type):
#     """Helper to create a single index asynchronously with retry logic"""
#     max_retries = 3
#     retry_delay = 2
    
#     for attempt in range(max_retries):
#         try:
#             # Test connection first
#             if sync_client:
#                 collections = sync_client.get_collections()
#                 logger.debug(f"Connection test successful for {index_type}")
            
#             # Create vector store
#             vector_store = QdrantVectorStore(
#                 aclient=async_client,
#                 client=sync_client,
#                 collection_name=collection_name
#             )
            
#             # Create storage context
#             storage = StorageContext.from_defaults(vector_store=vector_store)
            
#             # Create both sync and async indices
#             sync_index = VectorStoreIndex.from_vector_store(
#                 vector_store=vector_store,
#                 storage_context=storage,
#                 embed_model=embed_model
#             )
            
#             async_index = VectorStoreIndex.from_vector_store(
#                 vector_store=vector_store,
#                 storage_context=storage,
#                 embed_model=embed_model
#             )
            
#             logger.debug(f"Created {index_type} index for collection: {collection_name}")
#             return async_index, sync_index
            
#         except Exception as e:
#             logger.warning(f"Failed to create {index_type} index (attempt {attempt + 1}/{max_retries}): {e}")
            
#             if attempt < max_retries - 1:
#                 logger.info(f"Retrying {index_type} index creation in {retry_delay} seconds...")
#                 await asyncio.sleep(retry_delay)
#                 retry_delay *= 2  # Exponential backoff
#             else:
#                 logger.error(f"Failed to create {index_type} index after {max_retries} attempts: {e}")
#                 return None, None

# async def create_qdrant_clients(config_db):
#     """Create both async and sync Qdrant clients and indices"""
#     try:
#         # Get or create clients with connection pooling
#         async_client, sync_client = await get_or_create_qdrant_clients(config_db)
        
#         if not async_client or not sync_client:
#             return (None,) * 9  # Return 9 None values to match expected return count
            
#         # Create indices
#         indices = await create_qdrant_indices(async_client, sync_client, config_db)
        
#         if not any(indices):  # If all indices are None
#             return (None,) * 9
            
#         # Unpack indices and return with clients
#         qd_index_async, qd_index_sync, act_index, act_sync_index, \
#         summary_index, async_summary_index, constitution_index = indices
        
#         return (
#             async_client, sync_client, 
#             qd_index_async, qd_index_sync, 
#             act_index, act_sync_index, 
#             summary_index, async_summary_index,
#             constitution_index
#         )
        
#     except Exception as e:
#         logger.error(f"Failed in create_qdrant_clients: {e}")
#         return (None,) * 9
#         # embed_model = JinaEmbedding(
#         #     api_key=os.getenv("JINA_API_KEY"),
#         #     model="jina-embeddings-v4",
#         #     embed_batch_size=10,
#         #     task="text-matching",
#         #     dimensions=2048
#         # )
#         embed_model = OpenAIEmbedding(
#             api_key=os.getenv("OPENAI_API_KEY"),
#             model="text-embedding-3-large",
#             embed_batch_size=10,
#             task="text-matching",
#             dimensions=2048
#         )
#         embed_model1 = OpenAIEmbedding(
#             api_key=os.getenv("OPENAI_API_KEY"),
#             model="text-embedding-3-large",
#             embed_batch_size=10,
#             task="text-matching",
#             dimensions=1536
#         )
        
#         # Create async vector store and index
#         async_vector_store = QdrantVectorStore(
#             aclient=async_client, 
#             client=sync_client,
#             collection_name=sent_coll_name
#         )
#         async_storage = StorageContext.from_defaults(vector_store=async_vector_store)
#         async_index = VectorStoreIndex.from_vector_store(
#             vector_store=async_vector_store,
#             storage_context=async_storage,
#             embed_model=embed_model1
#         )
        
#         # Create sync vector store and index
#         sync_vector_store = QdrantVectorStore(
#             aclient=async_client,
#             client=sync_client,
#             collection_name=sent_coll_name

#         )
#         sync_storage = StorageContext.from_defaults(vector_store=sync_vector_store)
#         sync_index = VectorStoreIndex.from_vector_store(
#             vector_store=sync_vector_store,
#             storage_context=sync_storage,
#             embed_model=embed_model1
#         )
#         act_vector_store = QdrantVectorStore(
#             aclient=async_client,
#             client=sync_client,
#             collection_name=act_coll_name
#         )
     
#         act_storage = StorageContext.from_defaults(vector_store=act_vector_store)
#         act_index = VectorStoreIndex.from_vector_store(
#             vector_store=act_vector_store,
#             storage_context=act_storage,
#             embed_model=embed_model
#         )
        
#         # Create sync act index
#         act_sync_vector_store = QdrantVectorStore(
#             aclient=async_client,
#             client=sync_client,
#             collection_name=act_coll_name
#         )

      

#         act_sync_storage = StorageContext.from_defaults(vector_store=act_sync_vector_store)
#         act_sync_index = VectorStoreIndex.from_vector_store(
#             vector_store=act_sync_vector_store,
#             storage_context=act_sync_storage,
#             embed_model=embed_model
#         )
        

#         # summary index and async summary index

#         summary_vector_store = QdrantVectorStore(
#             aclient=async_client,
#             client=sync_client,
#             collection_name=summary_coll_name
#         )
#         summary_storage = StorageContext.from_defaults(vector_store=summary_vector_store)
#         summary_index = VectorStoreIndex.from_vector_store(
#             vector_store=summary_vector_store,
#             storage_context=summary_storage,
#             embed_model=embed_model
#         )
        
#         async_summary_index = VectorStoreIndex.from_vector_store(
#             vector_store=summary_vector_store,
#             storage_context=summary_storage,
#             embed_model=embed_model
#         )
#         constitution_split_vector_store = QdrantVectorStore(
#             aclient=async_client,
#             client=sync_client,
#             collection_name=constitution_split
#         )
#         constitution_split_storage = StorageContext.from_defaults(vector_store=constitution_split_vector_store)
#         constitution_split_index = VectorStoreIndex.from_vector_store(
#             vector_store=constitution_split_vector_store,
#             storage_context=constitution_split_storage,
#             embed_model=embed_model
#         )   


        
#         return async_client, sync_client, async_index, sync_index, act_index, act_sync_index,summary_index,async_summary_index, constitution_split_index

#     except Exception as e:
#         logger.error(f"Failed to create Qdrant client: {e}")
#         return None
 