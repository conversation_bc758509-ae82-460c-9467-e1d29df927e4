"""
Qdrant client for vector database operations (parallelized)
"""
from app.core.logger import StructuredLogger

from typing import Optional, <PERSON>ple
from qdrant_client import AsyncQdrantClient, QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.embeddings.openai import OpenAIEmbedding
import asyncio
import os

logger = StructuredLogger(__name__)

async def build_retriever(name: str, async_client, sync_client, embed_model, llm, collections):
    try:
        logger.info(f"🔄 Creating retriever for {name}...")
        store = QdrantVectorStore(
            aclient=async_client,
            client=sync_client,
            collection_name=collections[name],
        )
        vector_store_index = VectorStoreIndex.from_vector_store(
            vector_store=store,
            embed_model=embed_model,
            storage_context=StorageContext.from_defaults(vector_store=store),
        )
        retriever = vector_store_index.as_query_engine(llm=llm)
        logger.info(f"✅ Retriever created successfully for {name}")
        return retriever
    except Exception as e:
        logger.error(f"❌ Failed to create retriever {name}: {e}")
        return None


async def create_qdrant_client(config_db, llm_) -> Optional[Tuple]:
    try:
        logger.debug("🔄 Creating Qdrant client...")
        qdrant_config = await config_db.config.find_one({"name": "qdrant_config"})
        if not qdrant_config:
            logger.warning("Qdrant config not found in database")
            raise ValueError("Qdrant config not found in database")
        # choose llm
        MODEL=qdrant_config.get("llm_model","GEMINI")
        logger.info(f"🔄 Using {MODEL} for Qdrant Query Engine")
        print(llm_)
        if MODEL == "GEMINI":
            llm=llm_.gemini
        elif MODEL == "OPENAI":
            llm=llm_.openai
        host = qdrant_config["host"]
        port = qdrant_config["port"]

        # Clients
        async_client = AsyncQdrantClient(host=host, port=port, prefer_grpc=False, https=False)
        sync_client = QdrantClient(host=host, port=port, prefer_grpc=False, https=False)

        # Collection names
        collections = {
            "najir": qdrant_config.get("search_coll", "legal_sentence_split"),
            "act": qdrant_config.get("legal_act_coll", "legal_acts_jina"),
            "najir_summary": qdrant_config.get("najir_summary_col", "legal_summary_jina"),
            "constitution": qdrant_config.get("constitution_split", "constitution_split"),
        }

        # Embedding models
        embed_model_small = OpenAIEmbedding(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="text-embedding-3-large",
            embed_batch_size=10,
            task="text-matching",
            dimensions=1536,  # 👈 only for najir
        )

        embed_model_large = OpenAIEmbedding(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="text-embedding-3-large",
            embed_batch_size=10,
            task="text-matching",
            dimensions=2048,  # 👈 default for others
        )

        # Mapping retriever name -> correct embed model
        embed_map = {
            "najir": embed_model_small,
            "act": embed_model_large,
            "najir_summary": embed_model_large,
            "constitution": embed_model_large,
        }

        # Parallel retriever creation
        tasks = [
            build_retriever(name, async_client, sync_client, embed_map[name],llm, collections)
            for name in collections.keys()
        ]

        najir_retriever, act_retriever, najir_summary_retriever, constitution_retriever = await asyncio.gather(*tasks)

        return async_client, sync_client, najir_retriever, act_retriever, najir_summary_retriever, constitution_retriever

    except Exception as e:
        logger.error(f"❌ Failed to create Qdrant client: {e}")
        return None, None, None, None, None, None
